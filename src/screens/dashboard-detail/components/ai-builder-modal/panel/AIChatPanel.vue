<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import LLMPanel from '@/screens/chart-builder/llm-panel/LLMPanel.vue';
import AIChatPanelHeader from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatPanelHeader.vue';
import AIChatInput from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatInput.vue';
import AIConversation from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIConversation.vue';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';

@Component({
  components :{ AIConversation, AIChatInput, AIChatPanelHeader, LLMPanel }
})
export default class AIChatPanel extends Vue {
  protected isScrolled = false;
  protected mainData: ChatMessageData[] = [
    {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }
  ];

  mounted() {
    this.setupScrollListener();
  }

  beforeDestroy() {
    this.removeScrollListener();
  }

  private setupScrollListener() {
    const conversationElement = this.$el.querySelector('#ai-conversation');
    if (conversationElement) {
      conversationElement.addEventListener('scroll', this.handleScroll);
    }
  }

  private removeScrollListener() {
    const conversationElement = this.$el.querySelector('#ai-conversation');
    if (conversationElement) {
      conversationElement.removeEventListener('scroll', this.handleScroll);
    }
  }

  private handleScroll = (event: Event) => {
    const target = event.target as HTMLElement;
    const scrollTop = target.scrollTop;
    this.isScrolled = scrollTop > 10; // Threshold để kích hoạt blur
  }
}
</script>

<template>
  <div id="ai-chat-panel">
    <AIChatPanelHeader
      id="ai-chat-panel-header"
      :class="{ 'blurred': isScrolled }"
    />
    <AIConversation id="ai-conversation" :mainData="mainData" :botTyping="false" />
    <AIChatInput
      id="ai-chat-input"
      :class="{ 'blurred': isScrolled }"
    />
  </div>
</template>

<style scoped lang="scss">
#ai-chat-panel {
  height: 100%;
  position: relative;

  #ai-chat-panel-header {
    position: absolute;
    top: 0;
    z-index: 1;
    width: 100%;
    transition: backdrop-filter 0.3s ease, background-color 0.3s ease;

    &.blurred {
      backdrop-filter: blur(10px);
      background-color: rgba(255, 255, 255, 0.8);

      // Dark mode support
      @media (prefers-color-scheme: dark) {
        background-color: rgba(0, 0, 0, 0.8);
      }
    }
  }

  #ai-conversation {
    height: calc(100% - 100px);
    position: absolute;
    width: 100%;
    overflow-y: auto;
    padding-top: 60px; // Space for header
    padding-bottom: 60px; // Space for footer
    box-sizing: border-box;
  }

  #ai-chat-input {
    position: absolute;
    bottom: 0;
    z-index: 1;
    width: 100%;
    transition: backdrop-filter 0.3s ease, background-color 0.3s ease;

    &.blurred {
      backdrop-filter: blur(10px);
      background-color: rgba(255, 255, 255, 0.8);

      // Dark mode support
      @media (prefers-color-scheme: dark) {
        background-color: rgba(0, 0, 0, 0.8);
      }
    }
  }
}
</style>
