<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import LLMPanel from '@/screens/chart-builder/llm-panel/LLMPanel.vue';
import AIChatPanelHeader from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatPanelHeader.vue';
import AIChatInput from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatInput.vue';
import AIConversation from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIConversation.vue';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';

@Component({
  components: { AIConversation, AIChatInput, AIChatPanelHeader, LLMPanel }
})
export default class AIChatPanel extends Vue {
  protected mainData: ChatMessageData[] = [
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    }
  ];
}
</script>

<template>
  <div id="ai-chat-panel">
    <AIChatPanelHeader id="ai-chat-panel-header" />
    <AIConversation id="ai-conversation" :mainData="mainData" :botTyping="false" />
    <AIChatInput id="ai-chat-input" />
  </div>
</template>

<style scoped lang="scss">
#ai-chat-panel {
  height: 100%;
  position: relative;

  // Header với hiệu ứng blur gradient
  #ai-chat-panel-header {
    position: absolute;
    top: 0;
    z-index: 2;
    width: 100%;

    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      height: 20px;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 50%, rgba(255, 255, 255, 0) 100%);
      backdrop-filter: blur(8px);
      pointer-events: none;

      @media (prefers-color-scheme: dark) {
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 50%, rgba(0, 0, 0, 0) 100%);
      }
    }
  }

  #ai-conversation {
    height: calc(100% - 100px);
    position: absolute;
    width: 100%;
    top: 50px;
    bottom: 50px;
  }

  // Footer với hiệu ứng blur gradient
  #ai-chat-input {
    position: absolute;
    bottom: 0;
    z-index: 2;
    width: 100%;

    &::before {
      content: '';
      position: absolute;
      bottom: 100%;
      left: 0;
      right: 0;
      height: 20px;
      background: linear-gradient(to top, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 50%, rgba(255, 255, 255, 0) 100%);
      backdrop-filter: blur(8px);
      pointer-events: none;

      @media (prefers-color-scheme: dark) {
        background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 50%, rgba(0, 0, 0, 0) 100%);
      }
    }
  }
}
</style>
