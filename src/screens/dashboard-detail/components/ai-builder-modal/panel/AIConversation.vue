<template>
  <div class="di-board-content" ref="boardContent">
    <vuescroll>
      <div class="di-board-content__bubbles" ref="boardBubbles">
        <MessageBubble v-for="(item, index) in mainData" :key="index" :message="item"></MessageBubble>
        <div class="di-board-content__bot-typing" v-if="botTyping">
          <slot name="botTyping">
            <MessageTyping></MessageTyping>
          </slot>
        </div>
      </div>
    </vuescroll>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import MessageBubble from '@/screens/dashboard-detail/components/chatbot/components/MessageBubble/MessageBubble.vue';
import MessageTyping from '@/screens/dashboard-detail/components/chatbot/components/MessageBubble/MessageTyping.vue';

@Component({ components: { MessageBubble, MessageTyping } })
export default class AIConversation extends Vue {
  @Prop({ required: true }) mainData!: any[];
  @Prop({ default: false }) botTyping!: boolean;

  @Watch('mainData')
  onMainDataChanged(newVal: any) {
    this.$nextTick(() => {
      this.updateScroll();
    });
  }

  updateScroll() {
    const contentElm: any = this.$refs.boardContent;
    const offsetHeight = (this.$refs.boardBubbles as HTMLDivElement).offsetHeight;
    contentElm.scrollTop = offsetHeight;
  }
}
</script>

<style lang="scss" src="@/screens/dashboard-detail/components/chatbot/chatbot.scss" />
