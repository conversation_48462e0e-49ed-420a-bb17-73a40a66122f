/*
 * @author: tvc12 - Thi<PERSON> Vi
 * @created: 12/17/20, 11:53 AM
 */

import { WidgetCommonData } from '@core/common/domain/model';
import { Widget } from '@core/common/domain/model/widget/Widget';
import { FilterRequest } from '@core/common/domain/request';

export abstract class FilterWidget extends Widget {
  protected constructor(commonSetting: WidgetCommonData) {
    super(commonSetting);
  }

  abstract toFilterRequest(): FilterRequest | undefined;
}
