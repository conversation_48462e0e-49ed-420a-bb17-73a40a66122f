<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import LLMPanel from '@/screens/chart-builder/llm-panel/LLMPanel.vue';
import AIChatPanelHeader from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatPanelHeader.vue';
import AIChatInput from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatInput.vue';
import AIConversation from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIConversation.vue';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';

@Component({
  components :{ AIConversation, AIChatInput, AIChatPanelHeader, LLMPanel }
})
export default class AIChatPanel extends Vue {
  protected mainData: ChatMessageData[] = [
    {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }, {
      role :OpenAiMessageRole.user,
      type :MessageType.text,
      text :'Hello'
    },

    {
      role :OpenAiMessageRole.assistant,
      type :MessageType.text,
      text :'World'
    }
  ];
}
</script>

<template>
  <div id="ai-chat-panel">
    <AIChatPanelHeader id="ai-chat-panel-header" />
    <AIConversation id="ai-conversation" :mainData="mainData" :botTyping="false" />
    <AIChatInput id="ai-chat-input" />
  </div>
</template>

<style scoped lang="scss">
#ai-chat-panel {
  height: 100%;
  position: relative;

  #ai-chat-panel-header {
    position: absolute;
    top: 0;
    z-index: 1;
  }

  #ai-conversation {
    height: calc(100% - 100px);
    position: absolute;
    width: 100%;
  }

  #ai-chat-input {
    position: absolute;
    bottom: 0;
    z-index: 1;
  }
}
</style>
