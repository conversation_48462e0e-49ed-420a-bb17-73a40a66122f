import { Inject } from 'typescript-ioc';
import { LLMRepository } from '../repository/LLMRepository';
export abstract class LLMService {
  abstract generateChart(messages: string): Promise<string>;
}

export class LLMServiceImpl implements LLMService {
  @Inject
  private llmRepository!: LLMRepository;

  generateChart(messages: string): Promise<string> {
    return this.llmRepository.generateChart(messages);
  }
}

export class MockLLMService extends LLMService {
  generateChart(messages: string): Promise<string> {
    return Promise.resolve(
      `
I'll create a line chart showing profit over time using order dates.

\`\`\`
{
  "chart_type": "line",
  "query_setting": {
    "filters": [],
    "sorts": [],
    "options": {},
    "sql_views": [],
    "parameters": {},
    "x_axis": {
      "name": "Order Date",
      "function": {
        "field": {
          "class_name": "table_field",
          "db_name": "sample",
          "tbl_name": "sales",
          "field_name": "Order_Date",
          "field_type": "datetime"
        },
        "class_name": "group"
      },
      "is_horizontal_view": false,
      "is_collapse": false,
      "is_calc_group_total": true,
      "is_calc_min_max": false,
      "is_dynamic_function": false
    },
    "y_axis": [
      {
        "name": "Total Profit",
        "function": {
          "field": {
            "class_name": "table_field",
            "db_name": "sample",
            "tbl_name": "sales",
            "field_name": "Total_Profit",
            "field_type": "double"
          },
          "class_name": "sum"
        },
        "is_horizontal_view": false,
        "is_collapse": false,
        "is_calc_group_total": true,
        "is_calc_min_max": false,
        "is_dynamic_function": false
      }
    ],
    "legend": null,
    "class_name": "series_chart_setting"
  },
  "filter_requests": [],
  "from": -1,
  "size": -1,
  "dashboard_id": 64,
  "parameters": {}
}
\`\`\`
      `
    );
  }
}
