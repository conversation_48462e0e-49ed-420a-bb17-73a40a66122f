/*
 * @author: tvc12 - Thien Vi
 * @created: 12/3/20, 10:52 AM
 */

export enum QuerySettingClassName {
  Pie = 'pie_chart_setting',
  Funnel = 'funnel_chart_setting',
  Pyramid = 'pyramid_chart_setting',
  Series = 'series_chart_setting',
  Scatter = 'scatter_chart_setting',
  Bubble = 'bubble_chart_setting',
  HeatMap = 'heat_map_chart_setting',
  Gauge = 'gauge_chart_setting',
  Number = 'number_chart_setting',
  Drilldown = 'drilldown_chart_setting',
  WordCloud = 'word_cloud_chart_setting',
  Table = 'table_chart_setting',
  GroupedTable = 'group_table_chart_setting',
  TreeMap = 'tree_map_chart_setting',
  Histogram = 'histogram_chart_setting',
  Dropdown = 'dropdown_filter_chart_setting',
  TabFilter = 'tab_filter_chart_setting',
  Map = 'map_chart_setting',
  RawQuery = 'raw_query_setting',
  PivotTable = 'pivot_table_chart_setting',
  Parliament = 'parliament_chart_setting',
  SpiderWeb = 'spider_web_chart_setting',
  BellCurve = 'bell_curve_chart_setting',
  Sankey = 'sankey_chart_setting',
  FlattenPivot = 'flatten_pivot_table_chart_setting',
  Stocks = 'stocks_chart_setting',
  TabFilterQuerySetting = 'tab_control_chart_setting',
  InputQuerySetting = 'input_control_chart_setting',
  TreeFilter = 'tree_filter_chart_setting',
  GenericChart = 'generic_chart_setting'
}
